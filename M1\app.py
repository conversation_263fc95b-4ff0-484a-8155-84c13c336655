from flask import Flask, render_template, request, jsonify
import torch
from pathlib import Path
import cv2
import numpy as np
from darunet import DARU_Net
from data_preprocessing import DataPreprocessor
import matplotlib.pyplot as plt
import io
import base64

app = Flask(__name__)
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# Initialize model
model = DARU_Net()
try:
    checkpoint = torch.load('best_model.pth', map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    print("Model loaded successfully")
except Exception as e:
    print(f"Error loading model: {str(e)}")
model.to(device)
model.eval()

def preprocess_image(image, is_s1=True):
    # Resize image to match model input size
    image = cv2.resize(image, (256, 256))
    
    if is_s1:
        # Normalize S1 image
        image = image.astype(np.float32) / 255.0
        image = np.expand_dims(image, axis=0)  # Add channel dimension
    else:
        # Normalize S2 image and extract RGB channels
        image = image.astype(np.float32) / 255.0
        if len(image.shape) == 2:
            # If grayscale, convert to RGB
            image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        
        # Extract RGB channels and add NIR (Near-Infrared) channel
        # Using red channel as approximate NIR for demonstration
        rgb = image[:, :, :3]  # Extract RGB channels
        nir = image[:, :, 0]   # Use red channel as NIR approximation
        
        # Stack all channels (RGB + NIR)
        image = np.stack([
            rgb[:, :, 0],  # R
            rgb[:, :, 1],  # G
            rgb[:, :, 2],  # B
            nir           # NIR
        ])
    
    return image

@app.route('/')
def home():
    return render_template('index.html')

# Add this import at the top
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt

@app.route('/process', methods=['POST'])
def process_images():
    try:
        if 's1_image' not in request.files or 's2_image' not in request.files:
            return jsonify({'error': 'Both images are required'})
        
        s1_file = request.files['s1_image']
        s2_file = request.files['s2_image']
        
        # Validate file names
        if '_s1_' not in s1_file.filename.lower():
            return jsonify({'error': 'First image must be a Sentinel-1 image with "_s1_" in filename'})
        if '_s2_' not in s2_file.filename.lower():
            return jsonify({'error': 'Second image must be a Sentinel-2 image with "_s2_" in filename'})
        
        # Check if images are from the same pair
        s1_base = s1_file.filename.replace('_s1_', '_s2_')
        if s1_base != s2_file.filename:
            return jsonify({'error': 'Images do not match. Please ensure S1 and S2 images are from the same pair'})
        
        # Read images once
        s1_data = s1_file.read()
        s2_data = s2_file.read()
        
        if not s1_data or not s2_data:
            return jsonify({'error': 'Empty image data received'})
            
        # Decode images
        s1_array = np.frombuffer(s1_data, np.uint8)
        s2_array = np.frombuffer(s2_data, np.uint8)
        
        s1_img = cv2.imdecode(s1_array, cv2.IMREAD_GRAYSCALE)
        s2_img = cv2.imdecode(s2_array, cv2.IMREAD_COLOR)
        
        if s1_img is None or s2_img is None:
            return jsonify({'error': 'Failed to decode images. Please check the image format.'})
            
        # Validate image types
        if len(s1_img.shape) != 2:
            return jsonify({'error': 'First image must be a Sentinel-1 grayscale image'})
        if len(s2_img.shape) != 3:
            return jsonify({'error': 'Second image must be a Sentinel-2 RGB image'})
            
        # Check if images have matching dimensions
        if s1_img.shape != s2_img.shape[:2]:
            return jsonify({'error': 'Images dimensions do not match'})
        
        # Preprocess Sentinel-1
        s1_processed = DataPreprocessor().preprocess_sentinel1(s1_img)
        
        # Preprocess Sentinel-2
        s2_processed = DataPreprocessor().preprocess_sentinel2(s2_img)
        
        # Convert float images to uint8 before thresholding
        s1_uint8 = (s1_processed * 255).astype(np.uint8)
        s2_uint8 = (s2_processed[:,:,0] * 255).astype(np.uint8)  # Take first channel for thresholding
        
        # Create features for mask generation
        s1_features = cv2.threshold(s1_uint8, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
        s2_features = cv2.threshold(s2_uint8, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
        
        # Combine masks - Give more importance to Sentinel-2 (70% S2, 30% S1)
        combined_mask = cv2.addWeighted(s1_features, 0.3, s2_features, 0.7, 0)
        final_mask = (combined_mask > 127).astype(np.uint8) * 255
        
        # Convert mask to base64 for display
        _, buffer = cv2.imencode('.png', final_mask)
        mask_data = base64.b64encode(buffer).decode()
        
        # Calculate area (100m² per pixel, convert to km²)
        pixel_count = np.sum(final_mask > 0)
        area_km2 = (pixel_count * 100) / 1_000_000  # Assuming each pixel represents 100m²
        
        # Use model to get predictions for accuracy calculation
        s1_input = preprocess_image(s1_img, is_s1=True)
        s2_input = preprocess_image(s2_img, is_s1=False)
        
        # Convert to torch tensors and add batch dimension
        s1_tensor = torch.from_numpy(s1_input).unsqueeze(0).to(device)
        s2_tensor = torch.from_numpy(s2_input).unsqueeze(0).to(device)
        
        # Get model predictions
        with torch.no_grad():
            predictions = model(s1_tensor, s2_tensor)
            predictions = (predictions > 0.5).float()
        
        # Convert final_mask to tensor for comparison
        mask_tensor = torch.from_numpy((final_mask > 0).astype(np.float32)).to(device)
        
        # Calculate accuracy
        correct = (predictions.squeeze() == mask_tensor).float().sum()
        total = torch.numel(mask_tensor)
        accuracy = (correct / total) * 100
        
        # Calculate F1-score
        true_positives = (predictions.squeeze() * mask_tensor).sum()
        false_positives = (predictions.squeeze() * (1 - mask_tensor)).sum()
        false_negatives = ((1 - predictions.squeeze()) * mask_tensor).sum()
        
        precision = true_positives / (true_positives + false_positives + 1e-8)
        recall = true_positives / (true_positives + false_negatives + 1e-8)
        f1 = 2 * (precision * recall) / (precision + recall + 1e-8)
        
        # Create performance plot with matplotlib
        plt.switch_backend('Agg')
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Accuracy line plot
        x_points = np.array([0, 1])  # Two points for line
        accuracy_points = np.array([0, accuracy.cpu().item()])
        ax1.plot(x_points, accuracy_points, color='blue', marker='o', linewidth=2)
        ax1.set_ylim(0, 100)
        ax1.set_ylabel('Percentage (%)')
        ax1.set_title('Accuracy')
        ax1.set_xticks([0, 1])
        ax1.set_xticklabels(['Start', 'Current'])
        ax1.grid(True, linestyle='--', alpha=0.7)
        
        # F1-score line plot
        f1_points = np.array([0, f1.cpu().item()])
        ax2.plot(x_points, f1_points, color='green', marker='o', linewidth=2)
        ax2.set_ylim(0, 1)
        ax2.set_ylabel('Score')
        ax2.set_title('F1-Score')
        ax2.set_xticks([0, 1])
        ax2.set_xticklabels(['Start', 'Current'])
        ax2.grid(True, linestyle='--', alpha=0.7)
        
        plt.tight_layout()
        
        # Save plot to base64
        buf = io.BytesIO()
        fig.savefig(buf, format='png', dpi=300, bbox_inches='tight')
        plt.close(fig)
        buf.seek(0)
        plot_data = base64.b64encode(buf.getvalue()).decode()
        
        # Return results including the mask and plots
        return jsonify({
            'area': round(area_km2, 2),
            'accuracy': round(float(accuracy.cpu()), 2),
            'f1_score': round(float(f1.cpu()), 4),
            'plot': plot_data,
            'mask': mask_data
        })
        
    except Exception as e:
        print(f"Error processing images: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'An error occurred while processing the images: {str(e)}'})

if __name__ == '__main__':
    app.run(debug=True)